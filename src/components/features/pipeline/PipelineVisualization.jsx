/**
 * PipelineVisualization - Main component for displaying the multi-step pipeline
 * Shows visual pipeline steps with arrows, progress, and interactive controls
 */

import React, { useState, useEffect, useCallback } from 'react';
import PipelineStepCard from './PipelineStepCard.jsx';
import { PIPELINE_STEPS, STEP_STATUS, getNextAvailableSteps } from '../../../core/config/pipelineSteps.js';
import { documentProcessingPipeline } from '../../../services/DocumentProcessingPipeline.js';

const PipelineVisualization = ({
  file = null,
  isProcessing = false,
  onProcessingChange,
  onStepComplete,
  onError,
  autoRun = false
}) => {
  const [pipelineState, setPipelineState] = useState({
    steps: {},
    currentStep: null,
    completedSteps: [],
    errors: {},
    results: {},
    timings: {},
    overallProgress: 0
  });

  const [selectedOutput, setSelectedOutput] = useState(null);

  // Initialize pipeline state
  useEffect(() => {
    const initialSteps = {};
    PIPELINE_STEPS.forEach(step => {
      initialSteps[step.id] = {
        status: STEP_STATUS.PENDING,
        progress: 0,
        result: null,
        error: null,
        timing: 0
      };
    });

    setPipelineState(prev => ({
      ...prev,
      steps: initialSteps,
      currentStep: null,
      completedSteps: [],
      errors: {},
      results: {},
      timings: {},
      overallProgress: 0
    }));
  }, [file]);

  // Auto-run pipeline when file is provided
  useEffect(() => {
    if (file && autoRun && !isProcessing) {
      runFullPipeline();
    }
  }, [file, autoRun, isProcessing]);

  const updateStepState = useCallback((stepId, updates) => {
    setPipelineState(prev => ({
      ...prev,
      steps: {
        ...prev.steps,
        [stepId]: {
          ...prev.steps[stepId],
          ...updates
        }
      }
    }));
  }, []);

  const calculateOverallProgress = (completedSteps, currentStep, currentProgress) => {
    const totalSteps = PIPELINE_STEPS.length;
    const completedCount = completedSteps.length;
    const currentStepProgress = currentStep ? (currentProgress / 100) : 0;
    return Math.round(((completedCount + currentStepProgress) / totalSteps) * 100);
  };

  const runFullPipeline = async () => {
    if (!file || isProcessing) return;

    try {
      onProcessingChange?.(true);

      const progressCallback = (stepId, progress, message) => {
        updateStepState(stepId, {
          status: STEP_STATUS.RUNNING,
          progress: progress
        });

        setPipelineState(prev => ({
          ...prev,
          currentStep: stepId,
          overallProgress: calculateOverallProgress(prev.completedSteps, stepId, progress)
        }));
      };

      const result = await documentProcessingPipeline.processDocument(file, progressCallback);

      if (result.success) {
        // Update all steps as completed
        const finalSteps = { ...pipelineState.steps };
        const completedSteps = [];

        PIPELINE_STEPS.forEach(step => {
          const stepResult = result.stepResults?.[step.id];
          const hasError = stepResult?.error ||
                          stepResult?.success === false ||
                          stepResult?.metadata?.error ||
                          stepResult?.metadata?.extractionMethod === 'failed' ||
                          (stepResult?.confidence === 0 && stepResult?.success !== true);

          finalSteps[step.id] = {
            status: hasError ? STEP_STATUS.ERROR : STEP_STATUS.COMPLETED,
            progress: 100,
            result: stepResult || null,
            error: hasError ? (stepResult?.error || stepResult?.metadata?.error || 'Step failed') : null,
            timing: result.stepTimings?.[step.id] || 0
          };

          if (!hasError) {
            completedSteps.push(step.id);
          }
        });

        setPipelineState(prev => ({
          ...prev,
          steps: finalSteps,
          completedSteps,
          currentStep: null,
          overallProgress: 100,
          results: result.stepResults || {},
          timings: result.stepTimings || {}
        }));

        onStepComplete?.(result);
      } else {
        throw new Error(result.error || 'Pipeline processing failed');
      }
    } catch (error) {
      console.error('Pipeline execution error:', error);

      // Mark current step as error
      if (pipelineState.currentStep) {
        updateStepState(pipelineState.currentStep, {
          status: STEP_STATUS.ERROR,
          error: error.message
        });
      }

      onError?.(error.message);
    } finally {
      onProcessingChange?.(false);
    }
  };

  const runSingleStep = async (stepId) => {
    if (!file || isProcessing) return;

    try {
      onProcessingChange?.(true);
      updateStepState(stepId, {
        status: STEP_STATUS.RUNNING,
        progress: 0
      });

      const progressCallback = (progress) => {
        updateStepState(stepId, {
          progress: progress
        });
      };

      let result;
      switch (stepId) {
        case 'pdf_extraction':
          result = await documentProcessingPipeline.runPdfExtraction(file, { progressCallback });
          break;
        case 'deepseek_analysis':
          result = await documentProcessingPipeline.runDeepSeekAnalysis(file, { progressCallback });
          break;
        case 'rag_enhancement':
          result = await documentProcessingPipeline.runRAGEnhancement(file, { progressCallback });
          break;
        case 'tesseract_reference':
          result = await documentProcessingPipeline.runTesseractReference(file, { progressCallback });
          break;
        case 'field_mapping':
          result = await documentProcessingPipeline.runFieldMapping(file, { progressCallback });
          break;
        case 'data_validation':
          result = await documentProcessingPipeline.runDataValidation(file, { progressCallback });
          break;
        case 'final_output':
          result = await documentProcessingPipeline.runFinalOutput(file, { progressCallback });
          break;
        default:
          throw new Error(`Unknown step: ${stepId}`);
      }

      // Check if step actually succeeded
      const hasError = result.data?.error ||
                      result.data?.success === false ||
                      result.data?.metadata?.error ||
                      result.data?.metadata?.extractionMethod === 'failed' ||
                      (result.data?.confidence === 0 && result.data?.success !== true);

      if (result.success && !hasError) {
        updateStepState(stepId, {
          status: STEP_STATUS.COMPLETED,
          progress: 100,
          result: result.data,
          timing: result.timing || 0
        });

        setPipelineState(prev => ({
          ...prev,
          completedSteps: [...prev.completedSteps.filter(id => id !== stepId), stepId],
          results: {
            ...prev.results,
            [stepId]: result.data
          },
          timings: {
            ...prev.timings,
            [stepId]: result.timing || 0
          }
        }));

        onStepComplete?.({ stepId, result: result.data });
      } else {
        // Step failed
        const errorMessage = result.data?.error ||
                           result.data?.metadata?.error ||
                           result.error ||
                           'Step execution failed';

        updateStepState(stepId, {
          status: STEP_STATUS.ERROR,
          progress: 100,
          result: result.data,
          error: errorMessage,
          timing: result.timing || 0
        });

        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error(`Step ${stepId} execution error:`, error);
      updateStepState(stepId, {
        status: STEP_STATUS.ERROR,
        error: error.message
      });
      onError?.(error.message);
    } finally {
      onProcessingChange?.(false);
    }
  };

  const handleStepAction = (stepId, actionType) => {
    switch (actionType) {
      case 'rerun':
        runSingleStep(stepId);
        break;
      case 'view_raw':
      case 'view_output':
      case 'enhance_prompt':
      case 'view_similar':
      case 'compare_pdf':
      case 'view_mapping':
      case 'view_errors':
        setSelectedOutput({ stepId, actionType, data: pipelineState.results[stepId] });
        break;
      case 'export':
      case 'save':
        // Handle export/save actions
        console.log(`${actionType} action for step ${stepId}`);
        break;
      default:
        console.warn(`Unknown action: ${actionType}`);
    }
  };

  const renderPipelineArrow = (index) => {
    if (index === PIPELINE_STEPS.length - 1) return null;

    return (
      <div className="flex justify-center my-2">
        <div className="flex items-center">
          <div className="w-8 h-0.5 bg-gray-300"></div>
          <div className="w-0 h-0 border-l-4 border-l-gray-400 border-t-2 border-t-transparent border-b-2 border-b-transparent"></div>
        </div>
      </div>
    );
  };

  const renderOutputModal = () => {
    if (!selectedOutput) return null;

    const { stepId, actionType, data } = selectedOutput;
    const step = PIPELINE_STEPS.find(s => s.id === stepId);

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-2xl max-h-96 overflow-auto">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">
              {step?.name} - {actionType.replace('_', ' ').toUpperCase()}
            </h3>
            <button
              onClick={() => setSelectedOutput(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          <div className="text-sm">
            <pre className="bg-gray-100 p-3 rounded overflow-auto">
              {JSON.stringify(data, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full">
      {/* Pipeline Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900">
            Multi-Step Processing Pipeline
          </h3>
          <div className="flex items-center space-x-2">
            {file && (
              <button
                onClick={runFullPipeline}
                disabled={isProcessing}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:bg-gray-400"
              >
                {isProcessing ? '⏳ Processing...' : '🚀 Run Pipeline'}
              </button>
            )}
          </div>
        </div>

        {/* Overall Progress */}
        {pipelineState.overallProgress > 0 && (
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Overall Progress</span>
              <span>{pipelineState.overallProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${pipelineState.overallProgress}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Pipeline Steps */}
      <div className="space-y-1">
        {PIPELINE_STEPS.map((step, index) => (
          <div key={step.id}>
            <PipelineStepCard
              step={step}
              status={pipelineState.steps[step.id]?.status}
              progress={pipelineState.steps[step.id]?.progress}
              result={pipelineState.steps[step.id]?.result}
              error={pipelineState.steps[step.id]?.error}
              timing={pipelineState.steps[step.id]?.timing}
              onAction={handleStepAction}
              isActive={pipelineState.currentStep === step.id}
            />
            {renderPipelineArrow(index)}
          </div>
        ))}
      </div>

      {/* Output Modal */}
      {renderOutputModal()}
    </div>
  );
};

export default PipelineVisualization;
