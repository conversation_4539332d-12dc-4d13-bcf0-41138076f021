/**
 * Pipeline Steps Configuration
 * Defines the multi-step document processing pipeline with visual and functional metadata
 */

export const PIPELINE_STEPS = [
  {
    id: 'pdf_extraction',
    name: 'PDF Text Extraction',
    description: 'Extract text content from PDF using PDF.js',
    icon: '📄',
    color: 'blue',
    estimatedTime: 2000, // ms
    dependencies: [],
    outputs: ['text', 'pages', 'metadata'],
    actions: ['rerun', 'view_raw', 'view_output']
  },
  {
    id: 'deepseek_analysis',
    name: 'DeepSeek AI Analysis',
    description: 'Analyze document with AI for intelligent field extraction',
    icon: '🤖',
    color: 'purple',
    estimatedTime: 5000, // ms
    dependencies: ['pdf_extraction'],
    outputs: ['fields', 'confidence', 'analysis'],
    actions: ['rerun', 'view_raw', 'view_output', 'enhance_prompt']
  },
  {
    id: 'rag_enhancement',
    name: 'RAG Document Linking',
    description: 'Link similar documents and enhance prompts using RAG',
    icon: '🔗',
    color: 'green',
    estimatedTime: 3000, // ms
    dependencies: ['deepseek_analysis'],
    outputs: ['similar_docs', 'enhanced_prompt', 'context'],
    actions: ['rerun', 'view_raw', 'view_output', 'view_similar']
  },
  {
    id: 'tesseract_reference',
    name: 'OCR Structural Reference',
    description: 'Extract OCR text for structural validation and enhancement',
    icon: '👁️',
    color: 'orange',
    estimatedTime: 4000, // ms
    dependencies: ['pdf_extraction'],
    outputs: ['ocr_text', 'structure', 'confidence'],
    actions: ['rerun', 'view_raw', 'view_output', 'compare_pdf']
  },
  {
    id: 'field_mapping',
    name: 'Field Mapping',
    description: 'Map extracted fields using configuration and validation rules',
    icon: '🗺️',
    color: 'indigo',
    estimatedTime: 1000, // ms
    dependencies: ['deepseek_analysis', 'rag_enhancement', 'tesseract_reference'],
    outputs: ['mapped_fields', 'validation_results', 'corrections'],
    actions: ['rerun', 'view_raw', 'view_output', 'view_mapping']
  },
  {
    id: 'data_validation',
    name: 'Data Validation',
    description: 'Validate extracted data and calculate accuracy scores',
    icon: '✅',
    color: 'emerald',
    estimatedTime: 500, // ms
    dependencies: ['field_mapping'],
    outputs: ['validated_data', 'accuracy_score', 'errors'],
    actions: ['rerun', 'view_raw', 'view_output', 'view_errors']
  },
  {
    id: 'final_output',
    name: 'Final Output Generation',
    description: 'Generate final structured output with all enhancements',
    icon: '🎯',
    color: 'teal',
    estimatedTime: 500, // ms
    dependencies: ['data_validation'],
    outputs: ['final_result', 'summary', 'export_data'],
    actions: ['view_output', 'export', 'save']
  }
];

export const STEP_COLORS = {
  blue: {
    bg: 'bg-blue-50',
    border: 'border-blue-200',
    text: 'text-blue-800',
    icon: 'text-blue-600',
    progress: 'bg-blue-500'
  },
  purple: {
    bg: 'bg-purple-50',
    border: 'border-purple-200',
    text: 'text-purple-800',
    icon: 'text-purple-600',
    progress: 'bg-purple-500'
  },
  green: {
    bg: 'bg-green-50',
    border: 'border-green-200',
    text: 'text-green-800',
    icon: 'text-green-600',
    progress: 'bg-green-500'
  },
  orange: {
    bg: 'bg-orange-50',
    border: 'border-orange-200',
    text: 'text-orange-800',
    icon: 'text-orange-600',
    progress: 'bg-orange-500'
  },
  indigo: {
    bg: 'bg-indigo-50',
    border: 'border-indigo-200',
    text: 'text-indigo-800',
    icon: 'text-indigo-600',
    progress: 'bg-indigo-500'
  },
  emerald: {
    bg: 'bg-emerald-50',
    border: 'border-emerald-200',
    text: 'text-emerald-800',
    icon: 'text-emerald-600',
    progress: 'bg-emerald-500'
  },
  teal: {
    bg: 'bg-teal-50',
    border: 'border-teal-200',
    text: 'text-teal-800',
    icon: 'text-teal-600',
    progress: 'bg-teal-500'
  }
};

export const STEP_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  ERROR: 'error',
  SKIPPED: 'skipped'
};

export const ACTION_TYPES = {
  RERUN: 'rerun',
  VIEW_RAW: 'view_raw',
  VIEW_OUTPUT: 'view_output',
  ENHANCE_PROMPT: 'enhance_prompt',
  VIEW_SIMILAR: 'view_similar',
  COMPARE_PDF: 'compare_pdf',
  VIEW_MAPPING: 'view_mapping',
  VIEW_ERRORS: 'view_errors',
  EXPORT: 'export',
  SAVE: 'save'
};

/**
 * Get step configuration by ID
 */
export function getStepConfig(stepId) {
  return PIPELINE_STEPS.find(step => step.id === stepId);
}

/**
 * Get step dependencies
 */
export function getStepDependencies(stepId) {
  const step = getStepConfig(stepId);
  return step ? step.dependencies : [];
}

/**
 * Check if step can run based on completed dependencies
 */
export function canStepRun(stepId, completedSteps) {
  const dependencies = getStepDependencies(stepId);
  return dependencies.every(dep => completedSteps.includes(dep));
}

/**
 * Get next available steps that can run
 */
export function getNextAvailableSteps(completedSteps) {
  return PIPELINE_STEPS.filter(step => 
    !completedSteps.includes(step.id) && 
    canStepRun(step.id, completedSteps)
  );
}

/**
 * Calculate total estimated time for pipeline
 */
export function getTotalEstimatedTime() {
  return PIPELINE_STEPS.reduce((total, step) => total + step.estimatedTime, 0);
}

/**
 * Get step color configuration
 */
export function getStepColors(color) {
  return STEP_COLORS[color] || STEP_COLORS.blue;
}
